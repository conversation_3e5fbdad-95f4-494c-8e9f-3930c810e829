<?php
/**
 * Template para formulário de esqueceu a senha
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

// Obter configurações
$plugin = new CustomPasswordReset();
$errors = $plugin->get_errors();
$success = $plugin->get_success();
$settings = $plugin->get_settings();

// Obter logo
$logo_url = !empty($settings['logo_url']) ? $settings['logo_url'] : '';
if (empty($logo_url)) {
    $logo_id = get_theme_mod('custom_logo');
    $logo_url = $logo_id ? wp_get_attachment_image_src($logo_id, 'full')[0] : '';
}

// Obter URL de login
$login_url = wp_login_url();
if (function_exists('get_option')) {
    $login_page = get_option('rm_login_page', 'acessar');
    if (!empty($login_page)) {
        $login_url = site_url('/' . $login_page . '/');
    }
}
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex, nofollow">
    <title><?php echo esc_html($settings['title']); ?> - <?php bloginfo('name'); ?></title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Custom Styles Inline -->
    <style>
        <?php
        $css_file = dirname(__FILE__) . '/../assets/css/password-reset.css';
        if (file_exists($css_file)) {
            echo file_get_contents($css_file);
        }
        ?>

        /* Custom CSS Variables Override */
        :root {
            --cpr-primary-bg: <?php echo esc_attr($settings['background_color']); ?> !important;
            --cpr-card-bg: <?php echo esc_attr($settings['card_background']); ?> !important;
            --cpr-text-color: <?php echo esc_attr($settings['text_color']); ?> !important;
            --cpr-button-bg: <?php echo esc_attr($settings['button_color']); ?> !important;
            --cpr-button-text: <?php echo esc_attr($settings['button_text_color']); ?> !important;
        }

        <?php if (!empty($settings['custom_css'])): ?>
        /* Custom CSS */
        <?php echo $settings['custom_css']; ?>
        <?php endif; ?>
    </style>
    
    <?php wp_head(); ?>
</head>
<body class="cpr-password-reset cpr-theme-<?php echo esc_attr($settings['theme']); ?>">
    
    <div class="cpr-container">
        <!-- Card -->
        <div class="cpr-card">
            <!-- Logo dentro do card -->
            <div class="cpr-logo-container">
                <?php if (!empty($logo_url)): ?>
                    <img src="<?php echo esc_url($logo_url); ?>" alt="<?php bloginfo('name'); ?>" class="cpr-logo">
                <?php else: ?>
                    <div class="cpr-logo-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Título -->
            <h1 class="cpr-title"><?php echo esc_html($settings['title']); ?></h1>
            <p class="cpr-subtitle"><?php echo esc_html($settings['subtitle']); ?></p>
            
            <!-- Mensagens de erro -->
            <?php if (!empty($errors)): ?>
                <?php foreach ($errors as $error): ?>
                    <div class="cpr-message cpr-message-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo esc_html($error); ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <!-- Mensagem de sucesso -->
            <?php if (!empty($success)): ?>
                <div class="cpr-message cpr-message-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo esc_html($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- Formulário -->
            <form method="post" class="cpr-form" novalidate>
                <?php wp_nonce_field('cpr_lost_password', 'cpr_nonce'); ?>
                
                <div class="cpr-form-group">
                    <label for="user_login" class="cpr-label">
                        Nome de usuário ou endereço de e-mail
                    </label>
                    <input 
                        type="text" 
                        name="user_login" 
                        id="user_login" 
                        class="cpr-input" 
                        placeholder="E-mail corporativo"
                        required
                        autocomplete="username"
                        value="<?php echo esc_attr(isset($_POST['user_login']) ? $_POST['user_login'] : ''); ?>"
                    >
                </div>
                
                <button type="submit" name="cpr_lost_password_submit" class="cpr-button cpr-icon-<?php echo esc_attr($settings['button_icon_position']); ?>">
                    <?php if ($settings['button_icon_position'] === 'left' && $settings['button_icon'] !== 'none'): ?>
                        <i class="fas <?php echo esc_attr($settings['button_icon']); ?>"></i>
                    <?php endif; ?>

                    <span class="cpr-button-text"><?php echo esc_html($settings['button_text']); ?></span>

                    <?php if ($settings['button_icon_position'] === 'right' && $settings['button_icon'] !== 'none'): ?>
                        <i class="fas <?php echo esc_attr($settings['button_icon']); ?>"></i>
                    <?php endif; ?>
                </button>
            </form>
            
            <!-- Link para voltar ao login -->
            <div class="cpr-footer">
                <a href="<?php echo esc_url($login_url); ?>" class="cpr-link">
                    <i class="fas fa-arrow-left"></i>
                    <?php echo esc_html($settings['back_to_login_text']); ?>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Theme Toggle (será criado via JavaScript) -->
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Configurações para o JavaScript
        var cprSettings = {
            theme: '<?php echo esc_js($settings['theme']); ?>',
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('cpr_nonce'); ?>'
        };
    </script>
    <script src="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/js/password-reset.js'; ?>"></script>
    
    <?php wp_footer(); ?>
</body>
</html>
