<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

class AstraChildTheme {

    public function __construct() {
        // Ações de inicialização
        $this->init_hooks();
        
        // Carregar arquivos externos necessários
        add_action('elementor/init', [$this, 'load_external_files']);
    }

    /**
     * Inicializa os hooks do tema.
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_styles'], 20);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_font_awesome'], 10);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_custom_scripts']);
        add_action('init', [$this, 'disable_wp_emojicons']);
        add_action('after_setup_theme', [$this, 'remove_admin_bar_for_non_admins']);
        add_action('send_headers', [$this, 'adicionar_headers_seguranca']);
        
        // Filtros para plugins de cache
        add_filter('rocket_cache_reject_uri', [$this, 'add_wp_rocket_exclusion']);
        add_filter('w3tc_pgcache_exception_urls', [$this, 'add_w3_total_cache_exclusion']);
        add_filter('litespeed_cache_control', [$this, 'add_litespeed_cache_exclusion']);
        add_filter('cache_enabler_bypass_cache', [$this, 'add_cache_enabler_exclusion']);
        add_filter('sgo_bypass_cache', [$this, 'add_sg_optimizer_exclusion']);
        add_filter('wpfc_exclude_pages', [$this, 'add_wp_fastest_cache_exclusion']);
    }

    // Enfileirar o estilo do tema pai e do tema filho
    public function enqueue_styles() {
        // Enfileira o estilo do tema pai
        wp_enqueue_style('astra-style', get_template_directory_uri() . '/style.css');
    
        // Verifica se o arquivo style.css do tema filho existe antes de enfileirar
        $child_style_path = get_stylesheet_directory() . '/style.css';
        if (file_exists($child_style_path)) {
            wp_enqueue_style(
                'astra-child-style',
                get_stylesheet_directory_uri() . '/style.css',
                ['astra-style'], // Dependência no estilo do tema pai
                filemtime($child_style_path) // Versão baseada na modificação do arquivo
            );
        }
    }

    // Carregar a versão completa da Font Awesome com suporte a todos os ícones gratuitos
    public function enqueue_font_awesome() {
        wp_enqueue_style('font-awesome-all', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css', [], '6.6.0');
        wp_enqueue_style('font-awesome-core', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/fontawesome.min.css', [], '6.6.0');
        wp_enqueue_style('font-awesome-solid', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/solid.min.css', [], '6.6.0');
    }

    // Enfileirar scripts personalizados
    public function enqueue_custom_scripts() {
        wp_enqueue_script('astra-child-menu-mobile', get_stylesheet_directory_uri() . '/tutor/menu-mobile.js', ['jquery'], null, true);

        if (!is_singular('lesson')) {
            wp_enqueue_script('astra-child-global', get_stylesheet_directory_uri() . '/tutor/global.js', ['jquery'], null, true);
        }

        if (is_singular('lesson')) {
            wp_enqueue_script('astra-child-lesson', get_stylesheet_directory_uri() . '/tutor/lesson.js', ['jquery'], null, true);
        }
    }

    // Desabilitar emojis nativos do WordPress
    public function disable_wp_emojicons() {
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
        remove_action('admin_print_styles', 'print_emoji_styles');
        remove_filter('the_content_feed', 'wp_staticize_emoji');
        remove_filter('comment_text_rss', 'wp_staticize_emoji');
        remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
        add_filter('tiny_mce_plugins', [$this, 'disable_emojicons_tinymce']);
        add_filter('emoji_svg_url', '__return_false');
    }

    public function disable_emojicons_tinymce($plugins) {
        if (is_array($plugins)) {
            return array_diff($plugins, ['wpemoji']);
        }
        return [];
    }

    // Remove a barra de administração para usuários não administradores
    public function remove_admin_bar_for_non_admins() {
        if (!current_user_can('administrator')) {
            show_admin_bar(false);
        }
    }

    // Sem cache na página de login
    public function adicionar_headers_seguranca() {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: geolocation=()');
        header('Access-Control-Allow-Credentials: true');
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        
        // Adicionar header de segurança para cache em páginas específicas
        if (is_page(['acessar', 'cadastro'])) {
            header('Cache-Control: no-cache, no-store, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');
        }
    }

    // Filtros para plugins de cache

    public function add_wp_rocket_exclusion($uris) {
        $uris[] = '^/acessar/?';
        return $uris;
    }

    public function add_w3_total_cache_exclusion($urls) {
        $urls[] = home_url('/acessar/');
        return $urls;
    }

    public function add_litespeed_cache_exclusion($control) {
        if (is_page('acessar')) {
            $control['no-cache'] = true;
        }
        return $control;
    }

    public function add_cache_enabler_exclusion($bypass) {
        if (is_page('acessar')) {
            return true;
        }
        return $bypass;
    }

    public function add_sg_optimizer_exclusion($bypass) {
        if (is_page('acessar')) {
            return true;
        }
        return $bypass;
    }

    public function add_wp_fastest_cache_exclusion($exclude) {
        $exclude[] = '/acessar';
        return $exclude;
    }

    // Carregar arquivos externos necessários
    public function load_external_files() {
        
        $this->require_if_exists(get_stylesheet_directory() . '/custom-user/user-courses.php');
        $this->require_if_exists(get_stylesheet_directory() . '/custom-user/category-enrollment-sync.php');
        $this->require_if_exists(get_stylesheet_directory() . '/custom-user/tutor-export.php');
        $this->require_if_exists(get_stylesheet_directory() . '/custom-user/user-cpf-cnpj-whatsapp.php');
        $this->require_if_exists(get_stylesheet_directory() . '/modals/modal-scripts.php');
        $this->require_if_exists(get_stylesheet_directory() . '/custom-widgets/custom-carousel-widget.php');
        $this->require_if_exists(get_stylesheet_directory() . '/custom-widgets/custom-carousel-meus-cursos-widget.php');
        $this->require_if_exists(get_stylesheet_directory() . '/terms-of-use/terms-of-use.php');
        $this->require_if_exists(get_stylesheet_directory() . '/redirection-manager/redirection-manager.php');

        $this->require_if_exists(get_stylesheet_directory() . '/tutor/membero-pdf-shortcode.php');
        $this->require_if_exists(get_stylesheet_directory() . '/tutor/tutor-lms-custom-links.php');
        $this->require_if_exists(get_stylesheet_directory() . '/tutor/button-certificate-courses.php');

    }

    // Função auxiliar para incluir arquivos apenas se existirem
    private function require_if_exists($file) {
        if (file_exists($file)) {
            require_once $file;
        }
    }

    // Função auxiliar para verificar se a solicitação é isenta de redirecionamento
    private function is_exempted_request() {
        return is_admin() || wp_doing_ajax() || $this->is_elementor_request();
    }

    // Função auxiliar para verificar se a solicitação está relacionada ao Elementor
    private function is_elementor_request() {
        return isset($_GET['elementor-preview']) || isset($_GET['elementor-edit']) || (isset($_POST['action']) && strpos($_POST['action'], 'elementor') !== false);
    }
}

// Inicializa a classe do tema filho
new AstraChildTheme();


function dequeue_global_styles() {
    wp_dequeue_style('global-styles');  // Impede o enfileiramento
    wp_deregister_style('global-styles');  // Impede o registro
}
add_action('wp_enqueue_scripts', 'dequeue_global_styles', 20);



function enqueue_button_certificate_courses_styles() {
    wp_enqueue_style('button-certificate-courses', get_stylesheet_directory_uri() . '/assets/css/button-certificate-courses.css', array(), filemtime(get_stylesheet_directory() . '/assets/css/button-certificate-courses.css'));
}
add_action('wp_enqueue_scripts', 'enqueue_button_certificate_courses_styles');

// Redirecionamentos agora são gerenciados pelo plugin Redirection Manager


function restrict_wp_admin_access() {
    // Pega a URL atual
    $current_url = $_SERVER['REQUEST_URI'];
    
    // Permite acesso ao admin-ajax.php para chamadas AJAX públicas
    if (wp_doing_ajax()) {
        return;
    }
    
    // Verifica se está tentando acessar wp-admin
    if (strpos($current_url, '/wp-admin') !== false) {
        // Se não estiver logado, retorna (WordPress já lida com isso)
        if (!is_user_logged_in()) {
            return;
        }
        
        // Obtém o usuário atual
        $user = wp_get_current_user();
        
        // Lista de URLs permitidas mesmo para assinantes
        $allowed_urls = array(
            '/wp-admin/admin-ajax.php',
            '/wp-admin/profile.php'  // Permite que usuários editem seu próprio perfil
        );
        
        // Verifica se a URL atual está na lista de permitidas
        foreach ($allowed_urls as $allowed_url) {
            if (strpos($current_url, $allowed_url) !== false) {
                return;
            }
        }
        
        // Se for administrador, permite acesso
        if (current_user_can('administrator')) {
            return;
        }
        
        // Se for assinante, redireciona
        if (in_array('subscriber', (array) $user->roles)) {
            wp_safe_redirect(home_url());
            exit;
        }
    }
}

// Adiciona a função ao hook init para pegar todas as requisições
add_action('init', 'restrict_wp_admin_access', 1);


// Funções e hooks fora da classe
function enqueue_custom_carousels_assets() {
    // Enfileirando os estilos para o Carrossel
    wp_enqueue_style(
        'custom-carousel',
        get_stylesheet_directory_uri() . '/assets/css/custom-carousel.css',
        [],
        filemtime(get_stylesheet_directory() . '/assets/css/custom-carousel.css'),
        'all'
    );

    // Enfileirando o script para o Carrossel
    wp_enqueue_script(
        'custom-carousel',
        get_stylesheet_directory_uri() . '/assets/js/custom-carousel.js',
        ['jquery'],
        filemtime(get_stylesheet_directory() . '/assets/js/custom-carousel.js'),
        true
    );

    // Enfileirando os estilos para o Carrossel Meus Cursos
    wp_enqueue_style(
        'custom-carousel-meus-cursos',
        get_stylesheet_directory_uri() . '/assets/css/custom-carousel-meus-cursos.css',
        [],
        filemtime(get_stylesheet_directory() . '/assets/css/custom-carousel-meus-cursos.css'),
        'all'
    );

    // Enfileirando o script para o Carrossel Meus Cursos
    wp_enqueue_script(
        'custom-carousel-meus-cursos',
        get_stylesheet_directory_uri() . '/assets/js/custom-carousel-meus-cursos.js',
        ['jquery'],
        filemtime(get_stylesheet_directory() . '/assets/js/custom-carousel-meus-cursos.js'),
        true
    );

    // Enfileirando o estilo e script para o Modal
    wp_enqueue_style(
        'custom-carousel-modal', 
        get_stylesheet_directory_uri() . '/assets/css/custom-modal.css', 
        [], 
        filemtime(get_stylesheet_directory() . '/assets/css/custom-modal.css'), 
        'all'
    );
    wp_enqueue_script(
        'custom-carousel-modal', 
        get_stylesheet_directory_uri() . '/assets/js/custom-modal.js', 
        ['jquery'], 
        filemtime(get_stylesheet_directory() . '/assets/js/custom-modal.js'), 
        true
    );
}
add_action('wp_enqueue_scripts', 'enqueue_custom_carousels_assets', 5);


function enqueue_custom_script_for_lesson_pages() {
    // Verifica se a URL contém "/lesson/" para aplicar o CSS apenas nas páginas de aula
    if (strpos($_SERVER['REQUEST_URI'], '/lesson/') !== false) {
        wp_enqueue_script('custom-styles-js', get_stylesheet_directory_uri() . '/stylescss.js', array(), null, true);
    }
}
add_action('wp_enqueue_scripts', 'enqueue_custom_script_for_lesson_pages');


// Adicionar campo de URL da logo nas Configurações Gerais
function adicionar_campo_logo_url() {
    add_settings_field(
        'custom_logo_url', // ID do campo
        'URL da Logo do Email', // Rótulo do campo
        'renderizar_campo_logo_url', // Função de callback para renderizar o campo
        'general' // Página em que o campo será adicionado
    );

    register_setting('general', 'custom_logo_url', 'esc_url');
}
add_action('admin_init', 'adicionar_campo_logo_url');

// Função de callback para renderizar o campo de URL da logo
function renderizar_campo_logo_url() {
    $logo_url = get_option('custom_logo_url');
    echo '<input type="url" id="custom_logo_url" name="custom_logo_url" value="' . esc_attr($logo_url) . '" class="regular-text ltr" />';
}


function carregar_template_email($template_name, $placeholders = []) {
    $template_path = get_stylesheet_directory() . '/emails/' . $template_name . '.php';

    if (file_exists($template_path)) {
        $template = include $template_path;

        // Adicionar a logo do site como uma variável
        $logo_id = get_theme_mod('custom_logo');
        $logo_url = $logo_id ? wp_get_attachment_image_src($logo_id, 'full')[0] : '';
        
        // Substituir as variáveis do template
        foreach ($placeholders as $key => $value) {
            $template['titulo'] = str_replace('{' . $key . '}', $value, $template['titulo']);
            $template['resumo'] = str_replace('{' . $key . '}', $value, $template['resumo']);
            $template['mensagem'] = str_replace('{' . $key . '}', $value, $template['mensagem']);
            $template['rodape'] = str_replace('{' . $key . '}', $value, $template['rodape']);
        }

        return $template;
    }

    return false;
}


// Interceptar e-mail de Redefinição de Senha
// Carregar e aplicar o template do e-mail de redefinição de senha do PowerPack
function carregar_template_email_redefinicao_senha( $content, $user_data, $reset_url ) {
    // Caminho para o arquivo de template
    $template_path = get_stylesheet_directory() . '/emails/email_redefinicao_senha.php';

    if ( file_exists( $template_path ) ) {
        // Incluir o arquivo e recuperar a função de personalização
        $personalizar_email = include $template_path;

        // Verificar se é uma função válida e aplicá-la
        if ( is_callable( $personalizar_email ) ) {
            $result = $personalizar_email( $content, $user_data, $reset_url );

            // Atualizar o conteúdo e o título do e-mail
            if ( is_array( $result ) && isset( $result['content'], $result['subject'] ) ) {
                $content = $result['content'];
                $subject = $result['subject'];

                // Definir o título do e-mail usando o filtro específico do PowerPack
                add_filter( 'wp_mail', function( $args ) use ( $subject ) {
                    $args['subject'] = $subject;
                    return $args;
                } );
            }
        }
    }

    return $content;
}
add_filter( 'pp_login_form_password_reset_email_content', 'carregar_template_email_redefinicao_senha', 10, 3 );




function enqueue_swiper_assets() {
    // Carrega os arquivos do Swiper diretamente do cdnjs
    wp_enqueue_style('swiper-css', 'https://cdnjs.cloudflare.com/ajax/libs/Swiper/8.4.5/swiper-bundle.min.css');
    wp_enqueue_script('swiper-js', 'https://cdnjs.cloudflare.com/ajax/libs/Swiper/8.4.5/swiper-bundle.min.js', [], null, true);
}
add_action('wp_enqueue_scripts', 'enqueue_swiper_assets', 5);

// Adicionar menu de configurações
function protecao_conteudo_menu() {
    add_options_page(
        'Proteção de Conteúdo', // Título da página
        'Proteção de Conteúdo', // Nome do menu
        'manage_options',       // Permissão necessária
        'protecao-conteudo',    // Slug da página
        'protecao_conteudo_page' // Callback para renderizar a página
    );
}
add_action('admin_menu', 'protecao_conteudo_menu');

// Registrar configurações
function protecao_conteudo_settings() {
    register_setting(
        'protecao-conteudo-options', // Grupo de configurações
        'protecao_conteudo_ativada'  // Nome da opção
    );
}
add_action('admin_init', 'protecao_conteudo_settings');

// Renderizar a página de configurações
function protecao_conteudo_page() {
    ?>
    <div class="wrap">
        <h1>Proteção de Conteúdo</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('protecao-conteudo-options'); // Segurança e validação
            do_settings_sections('protecao-conteudo-options');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row">Proteção contra cópia</th>
                    <td>
                        <label class="protecao-toggle-switch">
                            <input type="checkbox" name="protecao_conteudo_ativada" value="1" <?php checked(1, get_option('protecao_conteudo_ativada'), true); ?>>
                            <span class="protecao-toggle-slider"></span>
                        </label>
                        <p class="description">Evite ações de clique com o botão direito, inspecionar, ver código fonte.</p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

// Verificar e adicionar proteção se ativada
function verificar_protecao_conteudo() {
    if (get_option('protecao_conteudo_ativada')) {
        add_action('wp_footer', 'adicionar_protecao_conteudo', 100);
    }
}
add_action('init', 'verificar_protecao_conteudo');

// Script de proteção no frontend
function adicionar_protecao_conteudo() {
    ?>
    <script>
(function() {
        document.addEventListener('keydown', function(e) {
            // Bloqueia F12
            if(e.key === 'F12' || e.keyCode === 123) {
                e.preventDefault();
                return false;
            }
            
            // Bloqueia Ctrl+Shift+I/C/J
            if(e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i' || e.key === 'C' || e.key === 'c' || e.key === 'J' || e.key === 'j')) {
                e.preventDefault();
                return false;
            }
            
            // Bloqueia Command+Option+I/C/J (Mac)
            if((e.metaKey && e.altKey) && 
               (e.key === 'I' || e.key === 'i' || e.keyCode === 73 ||
                e.key === 'C' || e.key === 'c' || e.keyCode === 67 ||
                e.key === 'J' || e.key === 'j' || e.keyCode === 74)) {
                e.preventDefault();
                return false;
            }
            
            // Bloqueia Command+Shift+C (Mac)
            if(e.metaKey && e.shiftKey && (e.key === 'C' || e.key === 'c' || e.keyCode === 67)) {
                e.preventDefault();
                return false;
            }

            // Bloqueia Ctrl+U e Command+U
            if((e.ctrlKey || e.metaKey) && (e.key === 'U' || e.key === 'u' || e.keyCode === 85)) {
                e.preventDefault();
                return false;
            }
        });

        // Resto do código permanece igual
        document.addEventListener('contextmenu', function(e) {
            const nodeName = e.target?.nodeName?.toUpperCase();
            if (!['INPUT', 'TEXTAREA'].includes(nodeName)) {
                e.preventDefault();
                return false;
            }
        });

        function detectDevTools() {
            if (window.devtools?.isOpen || window.Firebug?.chrome?.isInitialized) {
                window.location.reload(true);
            }
        }
        
        setInterval(detectDevTools, 1000);

        window.addEventListener('keypress', function(e) {
            if (e.keyCode === 123) {
                e.preventDefault();
                return false;
            }
        });

        window.addEventListener('keydown', function(e) {
            if (e.keyCode === 123) {
                e.preventDefault();
                return false;
            }
        });

        document.addEventListener('copy', function(e) {
            const nodeName = e.target?.nodeName?.toUpperCase();
            if (['INPUT', 'TEXTAREA'].includes(nodeName)) {
                return true;
            }
        });
    })();
    </script>
    <?php
}

// Enfileirar o CSS do botão de proteção
function carregar_estilos_protecao_botao() {
    if (is_admin() && isset($_GET['page']) && $_GET['page'] === 'protecao-conteudo') {
        wp_enqueue_style(
            'protecao-botao-css',
            get_stylesheet_directory_uri() . '/assets/css/protecao-botao.css',
            [],
            wp_get_theme()->get('Version')
        );
    }
}
add_action('admin_enqueue_scripts', 'carregar_estilos_protecao_botao');

function enqueue_protecao_toggle_css() {
    wp_enqueue_style('protecao-toggle-css', get_stylesheet_directory_uri() . '/assets/css/protecao-botao.css', array(), '1.0.0');
}
add_action('admin_enqueue_scripts', 'enqueue_protecao_toggle_css');


// Enfileira CSS e JS do Membero LMS
function enqueue_memberolms_assets($hook) {

    if (isset($_GET['page']) && $_GET['page'] === 'memberolms-settings') {
        // Debug para verificar caminhos dos arquivos
        $css_path = get_stylesheet_directory_uri() . '/assets/css/memberolms-admin.css';
        $js_path = get_stylesheet_directory_uri() . '/assets/js/memberolms-admin.js';

        // Verifica se os arquivos existem
        $css_file = get_stylesheet_directory() . '/assets/css/memberolms-admin.css';
        $js_file = get_stylesheet_directory() . '/assets/js/memberolms-admin.js';

        // Enfileira CSS
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_style(
            'memberolms-admin-styles',
            $css_path,
            array('wp-color-picker'),
            filemtime($css_file), // Usa o timestamp do arquivo como versão
            'all'
        );

        // Enfileira JS
        wp_enqueue_script(
            'memberolms-admin',
            $js_path,
            array('jquery', 'wp-color-picker'),
            filemtime($js_file), // Usa o timestamp do arquivo como versão
            true
        );

        // Adiciona variáveis para o script
        wp_localize_script(
            'memberolms-admin',
            'memberoLMSAdmin',
            array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('memberolms-settings-nonce'),
                // Adiciona uma variável para debug
                'isLoaded' => 'true'
            )
        );

    }
}
add_action('admin_enqueue_scripts', 'enqueue_memberolms_assets');


// Adicione isso ao seu functions.php
function tutor_lms_enqueue_drm_scripts() {
    if (is_singular('lesson')) {
        global $post;
        $course_id = tutor_utils()->get_course_id_by_lesson($post->ID);
        
        // Verifica se o arquivo existe antes de enfileirar
        $drm_script_path = get_stylesheet_directory() . '/tutor/tutor-drm-player.js';
        $version = file_exists($drm_script_path) ? filemtime($drm_script_path) : '1.0.0';
        
         wp_enqueue_script(
            'tutor-drm-player', 
            get_stylesheet_directory_uri() . '/tutor/tutor-drm-player.js',
            array('jquery'),
            time(), // Força atualização em desenvolvimento
            true
        );

        // Obtém os dados do usuário atual
        $current_user = wp_get_current_user();
        $user_id = $current_user->ID;

        // Prepara os dados do usuário
        wp_localize_script('tutor-drm-player', 'tutorUserInfo', array(
            'name' => $current_user->display_name,
            'email' => $current_user->user_email,
            'cpf_cnpj' => get_user_meta($user_id, 'cpf_cnpj', true),
            'whatsapp' => get_user_meta($user_id, 'whatsapp', true)
        ));

        // Prepara as configurações do overlay
        $opacity = get_post_meta($course_id, '_tutor_lms_custom_overlay_background_opacity', true);
        $opacity = $opacity === '' ? '1' : $opacity; // Preserva zero como string

        wp_localize_script('tutor-drm-player', 'tutorOverlaySettings', array(
            'enabled' => (bool) get_post_meta($course_id, '_tutor_lms_custom_overlay_enabled', true),
            'font_size' => get_post_meta($course_id, '_tutor_lms_custom_overlay_font_size', true) ?: '14',
            'background_color' => get_post_meta($course_id, '_tutor_lms_custom_overlay_background_color', true) ?: '#000000',
            'background_opacity' => $opacity,
            'display_option' => get_post_meta($course_id, '_tutor_lms_custom_overlay_display_option', true) ?: 'name_email'
        ));

    }
}
add_action('wp_enqueue_scripts', 'tutor_lms_enqueue_drm_scripts');

// Função para limpar restos do sistema de licenciamento removido
function cleanup_checker_user_system() {
    // Remove cron jobs relacionados ao sistema de licenciamento
    $timestamp = wp_next_scheduled('checker_user_cron_job');
    if ($timestamp) {
        wp_unschedule_event($timestamp, 'checker_user_cron_job');
    }

    // Remove transients relacionados ao sistema de licenciamento
    delete_transient('checker_user_license_status_global');
    delete_transient('checker_user_verification_lock');

    // Remove opções relacionadas ao sistema de licenciamento
    delete_option('checker_user_license_activated');
    delete_option('checker_user_purchase_email');

    // Remove meta de usuários relacionadas ao sistema
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'checker_user_%'");
}

// Executa a limpeza uma única vez após a remoção do sistema
function run_checker_user_cleanup_once() {
    if (!get_option('checker_user_cleanup_done', false)) {
        cleanup_checker_user_system();
        update_option('checker_user_cleanup_done', true);
    }
}
add_action('admin_init', 'run_checker_user_cleanup_once');





function monitor_database_connection_errors() {
    global $wpdb;

    // Verifica se houve erro de conexão
    if ( isset( $wpdb->error ) && strpos( $wpdb->error, 'max_user_connections' ) !== false ) {
        $error_message = sprintf(
            "[%s] Erro de Conexão: %s | Script: %s | Última Consulta: %s\n",
            date('Y-m-d H:i:s'),
            $wpdb->error,
            $_SERVER['SCRIPT_NAME'],
            $wpdb->last_query
        );

        // Define o caminho do arquivo de log
        $log_file = WP_CONTENT_DIR . '/logs/db_connection_errors.log';

        // Cria o diretório de logs se não existir
        if ( ! file_exists( dirname( $log_file ) ) ) {
            mkdir( dirname( $log_file ), 0755, true );
        }

        // Escreve a mensagem de erro no arquivo de log
        error_log( $error_message, 3, $log_file );
    }
}
add_action( 'plugins_loaded', 'monitor_database_connection_errors' );

function monitor_plugin_conflicts() {
    // Configura o caminho para o arquivo de log
    $log_file = WP_CONTENT_DIR . '/logs/plugin_conflicts.log';

    // Cria o diretório de logs se não existir
    if ( ! file_exists( dirname( $log_file ) ) ) {
        mkdir( dirname( $log_file ), 0755, true );
    }

    // Registra erros de PHP
    set_error_handler(function ($errno, $errstr, $errfile, $errline) use ($log_file) {
        $error_message = sprintf(
            "[%s] PHP Error: %s in %s on line %d\n",
            date('Y-m-d H:i:s'),
            $errstr,
            $errfile,
            $errline
        );
        error_log($error_message, 3, $log_file);
    });

    // Registra exceções não capturadas
    set_exception_handler(function ($exception) use ($log_file) {
        $error_message = sprintf(
            "[%s] Uncaught Exception: %s in %s on line %d\n",
            date('Y-m-d H:i:s'),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine()
        );
        error_log($error_message, 3, $log_file);
    });

    // Registra erros fatais (não capturados por set_error_handler)
    register_shutdown_function(function () use ($log_file) {
        $error = error_get_last();
        if ($error && ($error['type'] === E_ERROR || $error['type'] === E_PARSE || $error['type'] === E_CORE_ERROR || $error['type'] === E_COMPILE_ERROR)) {
            $error_message = sprintf(
                "[%s] Fatal Error: %s in %s on line %d\n",
                date('Y-m-d H:i:s'),
                $error['message'],
                $error['file'],
                $error['line']
            );
            error_log($error_message, 3, $log_file);
        }
    });

    // Registra problemas de carregamento de scripts e estilos
    add_action('wp_enqueue_scripts', function () use ($log_file) {
        global $wp_scripts, $wp_styles;

        foreach ($wp_scripts->queue as $handle) {
            if (empty($wp_scripts->registered[$handle]->src)) {
                $error_message = sprintf(
                    "[%s] Missing Script: %s\n",
                    date('Y-m-d H:i:s'),
                    $handle
                );
                error_log($error_message, 3, $log_file);
            }
        }

        foreach ($wp_styles->queue as $handle) {
            if (empty($wp_styles->registered[$handle]->src)) {
                $error_message = sprintf(
                    "[%s] Missing Style: %s\n",
                    date('Y-m-d H:i:s'),
                    $handle
                );
                error_log($error_message, 3, $log_file);
            }
        }
    });
}
add_action('init', 'monitor_plugin_conflicts');

// Força atualização da thumbnail do tema
function force_theme_screenshot_update() {
    // Limpa o cache da thumbnail do tema
    delete_transient('theme_roots');
    wp_clean_themes_cache();

    // Força regeneração do cache de temas
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }

    // Limpa cache específico de temas
    if (function_exists('wp_cache_delete')) {
        wp_cache_delete('themes', 'themes');
    }
}

// Executa a limpeza de cache quando necessário
add_action('after_switch_theme', 'force_theme_screenshot_update');

// Adiciona um hook para limpar cache manualmente
function clear_theme_cache_manual() {
    if (current_user_can('manage_options') && isset($_GET['clear_theme_cache'])) {
        force_theme_screenshot_update();
        wp_redirect(admin_url('themes.php?cache_cleared=1'));
        exit;
    }
}
add_action('admin_init', 'clear_theme_cache_manual');
