/**
 * Custom Password Reset Admin JavaScript
 */
(function($) {
    'use strict';

    class CPRAdmin {
        constructor() {
            this.init();
        }

        init() {
            this.setupColorPickers();
            this.setupTabs();
            this.setupConditionalFields();
            this.setupMediaUploader();
            this.setupFormHandling();
            this.setupPreview();
            this.setupKeyboardNavigation();
        }

        setupColorPickers() {
            // Inicializar color pickers do WordPress
            if ($.fn.wpColorPicker) {
                $('.cpr-color-picker').wpColorPicker({
                    change: () => {
                        this.updatePreview();
                    },
                    clear: () => {
                        this.updatePreview();
                    }
                });
            }
        }

        setupTabs() {
            // Navegação por abas
            $('.cpr-nav-tabs .nav-tab').on('click', (e) => {
                e.preventDefault();
                
                const $tab = $(e.currentTarget);
                const tabId = $tab.data('tab');
                
                this.switchTab(tabId);
            });

            // Carregar aba da URL
            this.loadTabFromHash();
            
            // Atualizar URL quando aba mudar
            $(window).on('hashchange', () => {
                this.loadTabFromHash();
            });
        }

        switchTab(tabId) {
            // Atualizar abas ativas
            $('.nav-tab').removeClass('nav-tab-active');
            $(`.nav-tab[data-tab="${tabId}"]`).addClass('nav-tab-active');
            
            // Mostrar conteúdo da aba
            $('.cpr-tab-content').hide();
            $(`#tab-${tabId}`).show();
            
            // Atualizar URL
            window.location.hash = tabId;
            
            // Trigger resize para color pickers
            $(window).trigger('resize');
        }

        loadTabFromHash() {
            const hash = window.location.hash.substring(1);
            if (hash && $(`.nav-tab[data-tab="${hash}"]`).length) {
                this.switchTab(hash);
            }
        }

        setupConditionalFields() {
            const toggleFields = () => {
                $('.cpr-conditional').each(function() {
                    const $row = $(this);
                    const condition = $row.data('condition');
                    const value = $row.data('value');
                    const $field = $(`#${condition}`);
                    
                    let shouldShow = false;
                    
                    if ($field.is(':checkbox')) {
                        shouldShow = ($field.is(':checked') && value == '1') || 
                                   (!$field.is(':checked') && value == '0');
                    } else {
                        shouldShow = $field.val() == value;
                    }
                    
                    if (shouldShow) {
                        $row.addClass('show');
                    } else {
                        $row.removeClass('show');
                    }
                });
            };
            
            // Executar ao carregar e quando campos mudarem
            toggleFields();
            $('input, select, textarea').on('change input', toggleFields);
        }

        setupMediaUploader() {
            $('.cpr-upload-button').on('click', (e) => {
                e.preventDefault();
                
                const $button = $(e.currentTarget);
                const targetField = $button.data('target');
                
                // Verificar se wp.media está disponível
                if (typeof wp === 'undefined' || !wp.media) {
                    alert('Media uploader não está disponível.');
                    return;
                }
                
                const mediaUploader = wp.media({
                    title: 'Selecionar Logo',
                    button: {
                        text: 'Usar esta imagem'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });
                
                mediaUploader.on('select', () => {
                    const attachment = mediaUploader.state().get('selection').first().toJSON();
                    $(`#${targetField}`).val(attachment.url).trigger('change');
                    this.updatePreview();
                });
                
                mediaUploader.open();
            });
        }

        setupFormHandling() {
            // Salvar configurações
            $('#cpr-settings-form').on('submit', (e) => {
                e.preventDefault();
                this.saveSettings();
            });
            
            // Restaurar padrões
            $('#cpr-reset-settings').on('click', () => {
                this.resetSettings();
            });
            
            // Auto-save em mudanças
            let saveTimeout;
            $('input, select, textarea').on('change input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.autoSave();
                }, 2000);
            });
        }

        saveSettings() {
            const $form = $('#cpr-settings-form');
            const $button = $('#cpr-save-settings');
            const $status = $('#cpr-status');
            
            $button.prop('disabled', true).find('.dashicons').addClass('spin');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: $form.serialize() + '&action=cpr_save_settings',
                success: (response) => {
                    if (response.success) {
                        this.showStatus('success', response.data);
                        this.updatePreview();
                    } else {
                        this.showStatus('error', response.data || 'Erro ao salvar configurações');
                    }
                },
                error: () => {
                    this.showStatus('error', 'Erro de conexão');
                },
                complete: () => {
                    $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                }
            });
        }

        autoSave() {
            const $form = $('#cpr-settings-form');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: $form.serialize() + '&action=cpr_save_settings&auto_save=1',
                success: (response) => {
                    if (response.success) {
                        this.showStatus('success', 'Salvo automaticamente', 1000);
                    }
                }
            });
        }

        resetSettings() {
            if (!confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
                return;
            }
            
            const $button = $('#cpr-reset-settings');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'cpr_reset_settings',
                    nonce: $('input[name="nonce"]').val()
                },
                success: (response) => {
                    if (response.success) {
                        this.showStatus('success', response.data);
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        this.showStatus('error', response.data || 'Erro ao restaurar configurações');
                    }
                },
                error: () => {
                    this.showStatus('error', 'Erro de conexão');
                },
                complete: () => {
                    $button.prop('disabled', false);
                }
            });
        }

        showStatus(type, message, duration = 3000) {
            const $status = $('#cpr-status');
            
            $status.removeClass('success error')
                   .addClass(type)
                   .text(message)
                   .show();
            
            setTimeout(() => {
                $status.fadeOut();
            }, duration);
        }

        setupPreview() {
            // Configurar controles de preview
            this.setupPreviewControls();

            // Atualizar preview quando campos mudarem
            $('input, select, textarea').on('change input', () => {
                clearTimeout(this.previewTimeout);
                this.previewTimeout = setTimeout(() => {
                    this.updatePreview();
                }, 1000); // Aumentar delay para evitar muitas atualizações
            });

            // Carregar preview inicial
            setTimeout(() => {
                this.updatePreview();
            }, 1500);
        }

        setupPreviewControls() {
            // Botão de atualizar preview
            $('#cpr-refresh-preview').on('click', () => {
                this.updatePreview();
            });

            // Botão de alternar tema no preview
            $('#cpr-toggle-preview-theme').on('click', () => {
                this.togglePreviewTheme();
            });

            // Controles de responsividade
            $('.cpr-preview-size').on('click', (e) => {
                e.preventDefault();
                const size = $(e.currentTarget).data('size');
                this.setPreviewSize(size);
            });
        }

        updatePreview() {
            const $frame = $('#cpr-preview-frame');
            if (!$frame.length) return;

            try {
                // Salvar configurações primeiro via AJAX
                this.saveSettingsForPreview().then(() => {
                    // Usar URL real do WordPress
                    const previewUrl = this.getPreviewUrl();

                    // Mostrar loading
                    this.showPreviewLoading();

                    // Carregar URL real no iframe
                    $frame.attr('src', previewUrl);

                    // Aguardar carregamento
                    $frame.on('load', () => {
                        this.hidePreviewLoading();
                        this.setupPreviewInteraction();
                    });

                }).catch((error) => {
                    console.error('Erro ao salvar configurações para preview:', error);
                    this.showPreviewError('Erro ao salvar configurações');
                });

            } catch (error) {
                console.error('Erro ao atualizar preview:', error);
                this.showPreviewError(error.message);
            }
        }

        getPreviewUrl() {
            // URL base do WordPress para reset de senha
            const baseUrl = window.location.origin + window.location.pathname.replace('/wp-admin/options-general.php', '/wp-login.php');

            // Adicionar parâmetros para preview
            const params = new URLSearchParams({
                action: 'lostpassword',
                cpr_preview: '1',
                cpr_timestamp: Date.now() // Evitar cache
            });

            return `${baseUrl}?${params.toString()}`;
        }

        saveSettingsForPreview() {
            return new Promise((resolve, reject) => {
                const $form = $('#cpr-settings-form');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: $form.serialize() + '&action=cpr_save_settings&preview=1',
                    success: (response) => {
                        if (response.success) {
                            resolve();
                        } else {
                            reject(new Error(response.data || 'Erro ao salvar'));
                        }
                    },
                    error: (xhr, status, error) => {
                        reject(new Error(`Erro AJAX: ${error}`));
                    }
                });
            });
        }

        showPreviewLoading() {
            const $container = $('.cpr-preview-container');
            $container.addClass('loading');

            if (!$container.find('.cpr-preview-loading').length) {
                $container.append(`
                    <div class="cpr-preview-loading">
                        <div class="spinner"></div>
                        <p>Carregando preview...</p>
                    </div>
                `);
            }
        }

        hidePreviewLoading() {
            $('.cpr-preview-container').removeClass('loading');
            $('.cpr-preview-loading').remove();
        }

        showPreviewError(message) {
            const $frame = $('#cpr-preview-frame');
            const errorHtml = `
                <div style="padding: 40px 20px; text-align: center; font-family: Arial, sans-serif; background: #f8f9fa;">
                    <div style="max-width: 400px; margin: 0 auto;">
                        <div style="width: 60px; height: 60px; background: #dc3545; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 24px;">⚠</span>
                        </div>
                        <h3 style="color: #dc3545; margin: 0 0 10px 0;">Preview Indisponível</h3>
                        <p style="color: #666; margin: 0 0 20px 0;">${message}</p>
                        <p style="font-size: 12px; color: #999;">
                            Teste as mudanças acessando diretamente:<br>
                            <code>wp-login.php?action=lostpassword</code>
                        </p>
                    </div>
                </div>
            `;

            const frameDoc = $frame[0].contentDocument || $frame[0].contentWindow.document;
            frameDoc.open();
            frameDoc.write(errorHtml);
            frameDoc.close();
        }

        setupPreviewInteraction() {
            // Adicionar controles de interação com o preview se necessário
            try {
                const $frame = $('#cpr-preview-frame');
                const frameWindow = $frame[0].contentWindow;

                // Verificar se conseguimos acessar o conteúdo do iframe
                if (frameWindow && frameWindow.document) {
                    // Adicionar classe para identificar que é preview
                    $(frameWindow.document.body).addClass('cpr-preview-mode');
                }
            } catch (error) {
                // Ignorar erros de CORS - é normal para iframes de domínios diferentes
                console.log('Preview carregado (sem acesso ao conteúdo devido a CORS)');
            }
        }

        collectSettings() {
            const settings = {};
            
            $('#cpr-settings-form').find('input, select, textarea').each(function() {
                const $field = $(this);
                const name = $field.attr('name');
                
                if (name && name.startsWith('settings[')) {
                    const key = name.replace('settings[', '').replace(']', '');
                    
                    if ($field.is(':checkbox')) {
                        settings[key] = $field.is(':checked');
                    } else {
                        settings[key] = $field.val();
                    }
                }
            });
            
            return settings;
        }

        generatePreviewHTML(settings) {
            const theme = settings.theme || 'dark';
            const logoUrl = settings.logo_url || '';
            const title = settings.title || 'Recuperar Acesso';
            const subtitle = settings.subtitle || 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.';
            const buttonText = settings.button_text || 'Enviar Instruções';

            // Carregar CSS inline
            let cssContent = '';
            try {
                // Aqui você pode carregar o CSS via AJAX se necessário
                // Por enquanto, vamos usar CSS básico inline
                cssContent = this.getBasicCSS(settings, theme);
            } catch (error) {
                console.warn('Erro ao carregar CSS:', error);
                cssContent = this.getBasicCSS(settings, theme);
            }

            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <title>Preview</title>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">
                    <style>
                        ${cssContent}

                        /* Custom overrides */
                        ${settings.custom_css || ''}
                    </style>
                </head>
                <body class="cpr-password-reset cpr-theme-${theme}">
                    <div class="cpr-container">
                        <div class="cpr-card">
                            <div class="cpr-logo-container">
                                ${logoUrl ?
                                    `<img src="${logoUrl}" alt="Logo" class="cpr-logo">` :
                                    `<div class="cpr-logo-icon"><i class="fas fa-envelope"></i></div>`
                                }
                            </div>
                            <h1 class="cpr-title">${title}</h1>
                            <p class="cpr-subtitle">${subtitle}</p>
                            <div class="cpr-form-group">
                                <label class="cpr-label">Nome de usuário ou endereço de e-mail</label>
                                <input type="email" class="cpr-input" placeholder="E-mail corporativo">
                            </div>
                            <button class="cpr-button">
                                <i class="fas fa-paper-plane"></i> ${buttonText}
                            </button>
                            <div class="cpr-footer" style="margin-top: 20px;">
                                <a href="#" class="cpr-link">
                                    <i class="fas fa-arrow-left"></i> Voltar ao Login
                                </a>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;
        }

        getBasicCSS(settings, theme) {
            const bgColor = settings.background_color || (theme === 'light' ? '#f5f5f5' : '#1a1a1a');
            const cardBg = settings.card_background || (theme === 'light' ? '#ffffff' : '#2d2d2d');
            const textColor = settings.text_color || (theme === 'light' ? '#333333' : '#ffffff');
            const buttonBg = settings.button_color || '#4a90e2';
            const buttonText = settings.button_text_color || '#ffffff';

            return `
                :root {
                    --cpr-primary-bg: ${bgColor};
                    --cpr-card-bg: ${cardBg};
                    --cpr-text-color: ${textColor};
                    --cpr-text-secondary: ${theme === 'light' ? '#666666' : '#b0b0b0'};
                    --cpr-button-bg: ${buttonBg};
                    --cpr-button-text: ${buttonText};
                    --cpr-button-hover: #357abd;
                    --cpr-input-bg: ${theme === 'light' ? '#ffffff' : '#3a3a3a'};
                    --cpr-input-border: ${theme === 'light' ? '#dddddd' : '#555555'};
                    --cpr-input-focus: #4a90e2;
                    --cpr-border-radius: 8px;
                    --cpr-shadow: ${theme === 'light' ? '0 4px 20px rgba(0, 0, 0, 0.1)' : '0 4px 20px rgba(0, 0, 0, 0.3)'};
                }

                * { box-sizing: border-box; }

                body.cpr-password-reset {
                    margin: 0; padding: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                    background-color: var(--cpr-primary-bg);
                    color: var(--cpr-text-color);
                    min-height: 100vh;
                    display: flex; align-items: center; justify-content: center;
                    transition: all 0.3s ease;
                }

                .cpr-container { width: 100%; max-width: 400px; padding: 20px; margin: 0 auto; }

                .cpr-logo-container { text-align: center; margin-bottom: 30px; }
                .cpr-logo { max-width: 60px; height: auto; margin-bottom: 0; }
                .cpr-logo-icon {
                    width: 60px; height: 60px; background: var(--cpr-button-bg);
                    border-radius: 12px; display: inline-flex; align-items: center;
                    justify-content: center; margin-bottom: 0; box-shadow: 0 2px 10px rgba(74, 144, 226, 0.3);
                }
                .cpr-logo-icon i { font-size: 24px; color: var(--cpr-button-text); }

                .cpr-card {
                    background: var(--cpr-card-bg); border-radius: var(--cpr-border-radius);
                    padding: 40px 30px; box-shadow: var(--cpr-shadow);
                    text-align: center; transition: all 0.3s ease;
                }

                .cpr-title {
                    font-size: 24px; font-weight: 600; color: var(--cpr-text-color);
                    margin: 0 0 12px 0; line-height: 1.3;
                }
                .cpr-subtitle {
                    font-size: 14px; color: var(--cpr-text-secondary);
                    margin: 0 0 30px 0; line-height: 1.5;
                }

                .cpr-form-group { margin-bottom: 20px; text-align: left; }
                .cpr-label {
                    display: block; font-size: 14px; font-weight: 500;
                    color: var(--cpr-text-color); margin-bottom: 8px;
                }
                .cpr-input {
                    width: 100%; padding: 14px 16px; font-size: 16px;
                    border: 2px solid var(--cpr-input-border); border-radius: var(--cpr-border-radius);
                    background-color: var(--cpr-input-bg); color: var(--cpr-text-color);
                    transition: all 0.3s ease; outline: none;
                }
                .cpr-input:focus {
                    border-color: var(--cpr-input-focus);
                    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
                }
                .cpr-input::placeholder { color: var(--cpr-text-secondary); }

                .cpr-button {
                    width: 100%; padding: 14px 20px; font-size: 16px; font-weight: 600;
                    border: none; border-radius: var(--cpr-border-radius);
                    background-color: var(--cpr-button-bg); color: var(--cpr-button-text);
                    cursor: pointer; transition: all 0.3s ease; margin-bottom: 20px;
                    text-transform: none; letter-spacing: 0;
                }
                .cpr-button:hover {
                    background-color: var(--cpr-button-hover);
                    transform: translateY(-1px);
                    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
                }

                .cpr-footer { text-align: center; margin-top: 30px; }
                .cpr-link {
                    color: var(--cpr-button-bg); text-decoration: none;
                    font-size: 14px; font-weight: 500; transition: all 0.3s ease;
                }
                .cpr-link:hover {
                    color: var(--cpr-button-hover); text-decoration: underline;
                }
            `;
        }

        applyPreviewStyles(frameDoc, settings) {
            // Método para aplicar estilos adicionais se necessário
            try {
                const body = frameDoc.body;
                if (body) {
                    body.style.opacity = '1';
                }
            } catch (error) {
                console.warn('Erro ao aplicar estilos do preview:', error);
            }
        }

        togglePreviewTheme() {
            // Alternar tema no preview
            const currentTheme = this.collectSettings().theme || 'dark';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            // Atualizar campo de tema
            $('#theme').val(newTheme).trigger('change');

            // Atualizar preview
            setTimeout(() => {
                this.updatePreview();
            }, 500);
        }

        setPreviewSize(size) {
            const $frame = $('#cpr-preview-frame');
            const $container = $('.cpr-preview-container');

            // Remover classes de tamanho anteriores
            $container.removeClass('preview-desktop preview-tablet preview-mobile');
            $('.cpr-preview-size').removeClass('active');

            // Adicionar nova classe e marcar botão ativo
            $container.addClass(`preview-${size}`);
            $(`.cpr-preview-size[data-size="${size}"]`).addClass('active');

            // Definir dimensões do iframe
            switch (size) {
                case 'desktop':
                    $frame.css({ width: '100%', height: '600px' });
                    break;
                case 'tablet':
                    $frame.css({ width: '768px', height: '600px' });
                    break;
                case 'mobile':
                    $frame.css({ width: '375px', height: '600px' });
                    break;
            }

            // Centralizar se necessário
            if (size !== 'desktop') {
                $container.css('text-align', 'center');
            } else {
                $container.css('text-align', 'left');
            }
        }

        setupKeyboardNavigation() {
            // Navegação por teclado nas abas
            $('.cpr-nav-tabs .nav-tab').on('keydown', (e) => {
                const $tabs = $('.cpr-nav-tabs .nav-tab');
                const $current = $(e.currentTarget);
                const currentIndex = $tabs.index($current);
                
                let newIndex = currentIndex;
                
                switch (e.key) {
                    case 'ArrowLeft':
                        newIndex = currentIndex > 0 ? currentIndex - 1 : $tabs.length - 1;
                        break;
                    case 'ArrowRight':
                        newIndex = currentIndex < $tabs.length - 1 ? currentIndex + 1 : 0;
                        break;
                    case 'Home':
                        newIndex = 0;
                        break;
                    case 'End':
                        newIndex = $tabs.length - 1;
                        break;
                    default:
                        return;
                }
                
                e.preventDefault();
                $tabs.eq(newIndex).focus().click();
            });
        }
    }

    // Inicializar quando o documento estiver pronto
    $(document).ready(() => {
        window.cprAdmin = new CPRAdmin();

        // Inicializar preview com tamanho desktop
        setTimeout(() => {
            if (window.cprAdmin && $('#tab-preview').length) {
                window.cprAdmin.setPreviewSize('desktop');
            }
        }, 500);
    });

})(jQuery);
