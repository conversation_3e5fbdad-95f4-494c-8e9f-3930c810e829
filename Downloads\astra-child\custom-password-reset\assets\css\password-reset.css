/* Custom Password Reset Styles */
:root {
    --cpr-primary-bg: #1a1a1a;
    --cpr-card-bg: #2d2d2d;
    --cpr-text-color: #ffffff;
    --cpr-text-secondary: #b0b0b0;
    --cpr-button-bg: #4a90e2;
    --cpr-button-text: #ffffff;
    --cpr-button-hover: #357abd;
    --cpr-input-bg: #3a3a3a;
    --cpr-input-border: #555555;
    --cpr-input-focus: #4a90e2;
    --cpr-error-color: #ff6b6b;
    --cpr-success-color: #51cf66;
    --cpr-border-radius: 8px;
    --cpr-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Light theme variables */
.cpr-theme-light {
    --cpr-primary-bg: #f5f5f5;
    --cpr-card-bg: #ffffff;
    --cpr-text-color: #333333;
    --cpr-text-secondary: #666666;
    --cpr-button-bg: #4a90e2;
    --cpr-button-text: #ffffff;
    --cpr-button-hover: #357abd;
    --cpr-input-bg: #ffffff;
    --cpr-input-border: #dddddd;
    --cpr-input-focus: #4a90e2;
    --cpr-error-color: #e74c3c;
    --cpr-success-color: #27ae60;
    --cpr-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Reset and base styles */
* {
    box-sizing: border-box;
}

body.cpr-password-reset {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--cpr-primary-bg);
    color: var(--cpr-text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

/* Main container */
.cpr-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    margin: 0 auto;
}

/* Logo container */
.cpr-logo-container {
    text-align: center;
    margin-bottom: 40px;
}

.cpr-logo {
    max-width: 80px;
    height: auto;
    margin-bottom: 20px;
}

.cpr-logo-icon {
    width: 80px;
    height: 80px;
    background: var(--cpr-button-bg);
    border-radius: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    box-shadow: var(--cpr-shadow);
}

.cpr-logo-icon i {
    font-size: 32px;
    color: var(--cpr-button-text);
}

/* Card */
.cpr-card {
    background: var(--cpr-card-bg);
    border-radius: var(--cpr-border-radius);
    padding: 40px 30px;
    box-shadow: var(--cpr-shadow);
    text-align: center;
    transition: all 0.3s ease;
}

/* Typography */
.cpr-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--cpr-text-color);
    margin: 0 0 12px 0;
    line-height: 1.3;
}

.cpr-subtitle {
    font-size: 14px;
    color: var(--cpr-text-secondary);
    margin: 0 0 30px 0;
    line-height: 1.5;
}

/* Form styles */
.cpr-form {
    width: 100%;
}

.cpr-form-group {
    margin-bottom: 20px;
    text-align: left;
}

.cpr-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--cpr-text-color);
    margin-bottom: 8px;
}

.cpr-input {
    width: 100%;
    padding: 14px 16px;
    font-size: 16px;
    border: 2px solid var(--cpr-input-border);
    border-radius: var(--cpr-border-radius);
    background-color: var(--cpr-input-bg);
    color: var(--cpr-text-color);
    transition: all 0.3s ease;
    outline: none;
}

.cpr-input:focus {
    border-color: var(--cpr-input-focus);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.cpr-input::placeholder {
    color: var(--cpr-text-secondary);
}

/* Button styles */
.cpr-button {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: var(--cpr-border-radius);
    background-color: var(--cpr-button-bg);
    color: var(--cpr-button-text);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    text-transform: none;
    letter-spacing: 0;
}

.cpr-button:hover {
    background-color: var(--cpr-button-hover);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
}

.cpr-button:active {
    transform: translateY(0);
}

.cpr-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Secondary button */
.cpr-button-secondary {
    background: transparent;
    color: var(--cpr-text-secondary);
    border: 2px solid var(--cpr-input-border);
    font-weight: 500;
}

.cpr-button-secondary:hover {
    background: var(--cpr-input-bg);
    color: var(--cpr-text-color);
    border-color: var(--cpr-input-focus);
    box-shadow: none;
}

/* Links */
.cpr-link {
    color: var(--cpr-button-bg);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.cpr-link:hover {
    color: var(--cpr-button-hover);
    text-decoration: underline;
}

/* Messages */
.cpr-message {
    padding: 12px 16px;
    border-radius: var(--cpr-border-radius);
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
}

.cpr-message-error {
    background-color: rgba(255, 107, 107, 0.1);
    border: 1px solid var(--cpr-error-color);
    color: var(--cpr-error-color);
}

.cpr-message-success {
    background-color: rgba(81, 207, 102, 0.1);
    border: 1px solid var(--cpr-success-color);
    color: var(--cpr-success-color);
}

/* Footer */
.cpr-footer {
    text-align: center;
    margin-top: 30px;
}

.cpr-footer-text {
    font-size: 12px;
    color: var(--cpr-text-secondary);
    margin: 0;
}

/* Loading state */
.cpr-loading {
    position: relative;
    overflow: hidden;
}

.cpr-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Theme toggle */
.cpr-theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--cpr-card-bg);
    border: 2px solid var(--cpr-input-border);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--cpr-shadow);
}

.cpr-theme-toggle:hover {
    border-color: var(--cpr-input-focus);
    transform: scale(1.05);
}

.cpr-theme-toggle i {
    font-size: 20px;
    color: var(--cpr-text-color);
}

/* Responsive design */
@media (max-width: 480px) {
    .cpr-container {
        padding: 15px;
    }
    
    .cpr-card {
        padding: 30px 20px;
    }
    
    .cpr-title {
        font-size: 22px;
    }
    
    .cpr-subtitle {
        font-size: 13px;
    }
    
    .cpr-input,
    .cpr-button {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .cpr-theme-toggle {
        top: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }
}

/* Animation for form appearance */
.cpr-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus visible for accessibility */
.cpr-button:focus-visible,
.cpr-input:focus-visible,
.cpr-theme-toggle:focus-visible {
    outline: 2px solid var(--cpr-input-focus);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --cpr-input-border: #000000;
        --cpr-text-secondary: #000000;
    }
    
    .cpr-theme-light {
        --cpr-input-border: #000000;
        --cpr-text-secondary: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
