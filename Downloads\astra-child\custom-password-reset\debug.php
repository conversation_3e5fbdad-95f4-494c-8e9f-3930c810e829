<?php
/**
 * Arquivo de debug para testar o Custom Password Reset
 */

// Verificar se estamos no WordPress
if (!defined('ABSPATH')) {
    // Se não estivermos no WordPress, simular algumas funções
    function esc_attr($text) { return htmlspecialchars($text, ENT_QUOTES, 'UTF-8'); }
    function esc_html($text) { return htmlspecialchars($text, ENT_HTML5, 'UTF-8'); }
    function esc_url($url) { return filter_var($url, FILTER_SANITIZE_URL); }
    function get_option($option, $default = false) {
        $options = [
            'cpr_theme' => 'dark',
            'cpr_logo_url' => '',
            'cpr_background_color' => '#1a1a1a',
            'cpr_card_background' => '#2d2d2d',
            'cpr_text_color' => '#ffffff',
            'cpr_button_color' => '#4a90e2',
            'cpr_button_text_color' => '#ffffff',
            'cpr_title' => 'Recuperar Acesso',
            'cpr_subtitle' => 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.',
            'cpr_button_text' => 'Enviar Instruções',
            'cpr_back_to_login_text' => 'Voltar ao Login',
            'cpr_custom_css' => ''
        ];
        return isset($options[$option]) ? $options[$option] : $default;
    }
}

// Simular configurações
$settings = [
    'theme' => get_option('cpr_theme', 'dark'),
    'logo_url' => get_option('cpr_logo_url', ''),
    'background_color' => get_option('cpr_background_color', '#1a1a1a'),
    'card_background' => get_option('cpr_card_background', '#2d2d2d'),
    'text_color' => get_option('cpr_text_color', '#ffffff'),
    'button_color' => get_option('cpr_button_color', '#4a90e2'),
    'button_text_color' => get_option('cpr_button_text_color', '#ffffff'),
    'button_icon' => get_option('cpr_button_icon', 'fa-arrow-right'),
    'button_icon_position' => get_option('cpr_button_icon_position', 'right'),
    'title' => get_option('cpr_title', 'Recuperar Acesso'),
    'subtitle' => get_option('cpr_subtitle', 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.'),
    'button_text' => get_option('cpr_button_text', 'Enviar Instruções'),
    'back_to_login_text' => get_option('cpr_back_to_login_text', 'Voltar ao Login'),
    'success_message' => get_option('cpr_success_message', 'Instruções de redefinição de senha foram enviadas para seu e-mail.'),
    'custom_css' => get_option('cpr_custom_css', '')
];

$logo_url = $settings['logo_url'];
$login_url = '#';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex, nofollow">
    <title><?php echo esc_html($settings['title']); ?> - Debug</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">
    
    <!-- Custom Styles Inline -->
    <style>
        <?php 
        $css_file = __DIR__ . '/assets/css/password-reset.css';
        if (file_exists($css_file)) {
            echo file_get_contents($css_file);
        } else {
            echo "/* CSS file not found: $css_file */";
        }
        ?>
        
        /* Custom CSS Variables Override */
        :root {
            --cpr-primary-bg: <?php echo esc_attr($settings['background_color']); ?> !important;
            --cpr-card-bg: <?php echo esc_attr($settings['card_background']); ?> !important;
            --cpr-text-color: <?php echo esc_attr($settings['text_color']); ?> !important;
            --cpr-button-bg: <?php echo esc_attr($settings['button_color']); ?> !important;
            --cpr-button-text: <?php echo esc_attr($settings['button_text_color']); ?> !important;
        }
        
        <?php if (!empty($settings['custom_css'])): ?>
        /* Custom CSS */
        <?php echo $settings['custom_css']; ?>
        <?php endif; ?>
        
        /* Debug info */
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1000;
            max-width: 300px;
        }
        .debug-info h4 {
            margin: 0 0 10px 0;
            color: #4a90e2;
        }
        .debug-info p {
            margin: 5px 0;
        }
    </style>
</head>
<body class="cpr-password-reset cpr-theme-<?php echo esc_attr($settings['theme']); ?>">
    
    <!-- Debug Info -->
    <div class="debug-info">
        <h4>Debug Info</h4>
        <p><strong>Tema:</strong> <?php echo $settings['theme']; ?></p>
        <p><strong>CSS File:</strong> <?php echo file_exists($css_file) ? 'Found' : 'Not Found'; ?></p>
        <p><strong>Background:</strong> <?php echo $settings['background_color']; ?></p>
        <p><strong>Card BG:</strong> <?php echo $settings['card_background']; ?></p>
        <p><strong>Text Color:</strong> <?php echo $settings['text_color']; ?></p>
        <p><strong>Button Color:</strong> <?php echo $settings['button_color']; ?></p>
    </div>
    
    <div class="cpr-container">
        <!-- Card -->
        <div class="cpr-card">
            <!-- Logo dentro do card -->
            <div class="cpr-logo-container">
                <?php if (!empty($logo_url)): ?>
                    <img src="<?php echo esc_url($logo_url); ?>" alt="Logo" class="cpr-logo">
                <?php else: ?>
                    <div class="cpr-logo-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Título -->
            <h1 class="cpr-title"><?php echo esc_html($settings['title']); ?></h1>
            <p class="cpr-subtitle"><?php echo esc_html($settings['subtitle']); ?></p>
            
            <!-- Mensagem de teste -->
            <div class="cpr-message cpr-message-success" style="display: none;">
                <i class="fas fa-check-circle"></i>
                <?php echo esc_html($settings['success_message']); ?>
            </div>
            
            <!-- Formulário -->
            <form class="cpr-form" onsubmit="return false;">
                <div class="cpr-form-group">
                    <label for="user_login" class="cpr-label">
                        Nome de usuário ou endereço de e-mail
                    </label>
                    <input 
                        type="text" 
                        name="user_login" 
                        id="user_login" 
                        class="cpr-input" 
                        placeholder="E-mail corporativo"
                        required
                    >
                </div>
                
                <button type="submit" class="cpr-button cpr-icon-<?php echo esc_attr($settings['button_icon_position']); ?>">
                    <?php if ($settings['button_icon_position'] === 'left' && $settings['button_icon'] !== 'none'): ?>
                        <i class="fas <?php echo esc_attr($settings['button_icon']); ?>"></i>
                    <?php endif; ?>

                    <span class="cpr-button-text"><?php echo esc_html($settings['button_text']); ?></span>

                    <?php if ($settings['button_icon_position'] === 'right' && $settings['button_icon'] !== 'none'): ?>
                        <i class="fas <?php echo esc_attr($settings['button_icon']); ?>"></i>
                    <?php endif; ?>
                </button>
            </form>
            
            <!-- Link para voltar ao login -->
            <div class="cpr-footer">
                <a href="<?php echo esc_url($login_url); ?>" class="cpr-link">
                    <i class="fas fa-arrow-left"></i>
                    <?php echo esc_html($settings['back_to_login_text']); ?>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Configurações para o JavaScript
        var cprSettings = {
            theme: '<?php echo esc_js($settings['theme']); ?>',
            ajaxUrl: '#',
            nonce: 'debug'
        };
        
        // Controles de teste
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.classList.contains('cpr-theme-dark') ? 'dark' : 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            body.classList.remove('cpr-theme-dark', 'cpr-theme-light');
            body.classList.add('cpr-theme-' + newTheme);
            
            // Atualizar debug info
            document.querySelector('.debug-info p').innerHTML = '<strong>Tema:</strong> ' + newTheme;
        }
        
        function showMessage() {
            $('.cpr-message').show().delay(3000).fadeOut();
        }

        // Variáveis para teste
        let currentIconIndex = 1; // Começa com fa-arrow-right
        let currentPosition = 'right';

        const icons = [
            'fa-paper-plane',
            'fa-arrow-right',
            'fa-envelope',
            'fa-send',
            'fa-check',
            'fa-play',
            'fa-chevron-right'
        ];

        function toggleIcon() {
            currentIconIndex = (currentIconIndex + 1) % icons.length;
            const newIcon = icons[currentIconIndex];

            // Atualizar ícone no botão
            $('.cpr-button i').removeClass().addClass('fas ' + newIcon);

            console.log('Ícone alterado para:', newIcon);
        }

        function toggleIconPosition() {
            currentPosition = currentPosition === 'right' ? 'left' : 'right';
            const $button = $('.cpr-button');

            // Remover classes antigas
            $button.removeClass('cpr-icon-left cpr-icon-right');

            // Adicionar nova classe
            $button.addClass('cpr-icon-' + currentPosition);

            console.log('Posição do ícone alterada para:', currentPosition);
        }
        
        // Adicionar controles
        $(document).ready(function() {
            const controls = $(`
                <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 8px; z-index: 1000; max-width: 200px;">
                    <h4 style="margin: 0 0 10px 0; color: #4a90e2;">Controles de Teste</h4>
                    <button onclick="toggleTheme()" style="margin: 2px; padding: 6px 10px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; width: 100%;">
                        Alternar Tema
                    </button>
                    <button onclick="showMessage()" style="margin: 2px; padding: 6px 10px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; width: 100%;">
                        Mostrar Mensagem
                    </button>
                    <button onclick="toggleIcon()" style="margin: 2px; padding: 6px 10px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; width: 100%;">
                        Alternar Ícone
                    </button>
                    <button onclick="toggleIconPosition()" style="margin: 2px; padding: 6px 10px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; width: 100%;">
                        Posição Ícone
                    </button>
                </div>
            `);
            $('body').append(controls);
            
            // Simular envio do formulário
            $('.cpr-form').on('submit', function(e) {
                e.preventDefault();
                const $button = $('.cpr-button');
                
                $button.addClass('cpr-loading').text('Enviando...');
                
                setTimeout(() => {
                    $button.removeClass('cpr-loading').html('<i class="fas fa-paper-plane"></i> <?php echo esc_js($settings['button_text']); ?>');
                    showMessage();
                }, 2000);
            });
        });
    </script>
    
    <?php if (file_exists(__DIR__ . '/assets/js/password-reset.js')): ?>
    <script src="assets/js/password-reset.js"></script>
    <?php endif; ?>
</body>
</html>
